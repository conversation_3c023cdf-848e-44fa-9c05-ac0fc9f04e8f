<template>
    <div class="survey-design">
      <el-card>
        <!-- 问卷标题/描述 -->
        <el-input v-model="survey.title" placeholder="问卷标题" style="width: 100%; margin-bottom: 10px;" />
        <el-input
          v-model="survey.description"
          type="textarea"
          placeholder="问卷描述（可选）"
          :rows="2"
          style="width: 100%; margin-bottom: 20px;"
        />
  
        <!-- 题型选择 -->
        <div class="question-types">
          <el-button 
            v-for="type in questionTypes" 
            :key="type.value" 
            @click="addQuestion(type.value)" 
            type="primary" 
            plain
          >{{ type.label }}</el-button>
        </div>
  
        <!-- 问题列表 -->
        <div class="questions">
          <div v-for="(q, idx) in survey.questions" :key="q.id" class="question-item">
            <el-card>
              <div class="question-header">
                <span>{{ idx + 1 }}.</span>
                <el-input v-model="q.title" placeholder="问题" style="flex: 1;" />
                <el-select v-model="q.type" @change="updateType(idx)">
                  <el-option 
                    v-for="t in questionTypes" 
                    :key="t.value" 
                    :label="t.label" 
                    :value="t.value"
                  />
                </el-select>
                <el-checkbox v-model="q.required">必填</el-checkbox>
                <el-button icon="Delete" @click="removeQuestion(idx)" type="danger" />
              </div>
  
              <!-- 选项（单选/多选） -->
              <div v-if="q.type === 'radio' || q.type === 'checkbox'">
                <div v-for="(opt, oIdx) in q.options" :key="opt.id" class="option-item">
                  <el-input v-model="opt.label" placeholder="选项" style="flex: 1;" />
                  <el-button icon="Delete" @click="removeOption(idx, oIdx)" type="danger" />
                </div>
                <el-button icon="Plus" @click="addOption(idx)" type="success" plain>添加选项</el-button>
              </div>
  
              <!-- 文本题占位符 -->
              <div v-if="q.type === 'text'">
                <el-input 
                  type="textarea" 
                  v-model="q.placeholder" 
                  placeholder="示例回答（可选）" 
                  :rows="2" 
                  style="width: 100%;"
                />
              </div>
            </el-card>
          </div>
        </div>
  
        <!-- 操作按钮 -->
        <div class="actions">
          <el-button @click="save">保存</el-button>
          <el-button type="primary" @click="publish" v-if="survey.id">发布</el-button>
          <el-button @click="router.back()">返回</el-button>
        </div>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useSurveyStore } from '../store';
  
  const router = useRouter();
  const route = useRoute();
  const surveyStore = useSurveyStore();
  
  const survey = ref({
    id: route.params.id || Date.now().toString(),
    title: '',
    description: '',
    createTime: new Date().toLocaleString(),
    status: route.params.id ? 'published' : 'draft',
    questions: [],
  });
  
  const questionTypes = ref([
    { label: '单选题', value: 'radio' },
    { label: '多选题', value: 'checkbox' },
    { label: '文本题', value: 'text' },
  ]);
  
  // 页面加载时，若为“编辑”则拉取已有问卷
  onMounted(() => {
    if (route.params.id) {
      const found = surveyStore.getSurveyById(route.params.id);
      if (found) survey.value = JSON.parse(JSON.stringify(found));
    }
  });
  
  // 添加问题
  const addQuestion = (type) => {
    const newQ = {
      id: Date.now().toString(),
      type,
      title: '',
      required: false,
      options: type === 'radio' || type === 'checkbox' 
        ? [{ id: Date.now().toString(), label: '' }] 
        : [],
      placeholder: type === 'text' ? '' : '',
    };
    survey.value.questions.push(newQ);
  };
  
  // 删除问题
  const removeQuestion = (idx) => survey.value.questions.splice(idx, 1);
  
  // 添加选项
  const addOption = (qIdx) => {
    const newOpt = { id: Date.now().toString(), label: '' };
    survey.value.questions[qIdx].options.push(newOpt);
  };
  
  // 删除选项
  const removeOption = (qIdx, oIdx) => 
    survey.value.questions[qIdx].options.splice(oIdx, 1);
  
  // 切换题型
  const updateType = (qIdx) => {
    const q = survey.value.questions[qIdx];
    if (q.type === 'radio' || q.type === 'checkbox') {
      if (!q.options || q.options.length === 0) {
        q.options = [{ id: Date.now().toString(), label: '' }];
      }
    } else {
      delete q.options;
      q.placeholder = '';
    }
  };
  
  // 保存问卷
  const save = () => {
    survey.value.updateTime = new Date().toLocaleString();
    if (route.params.id) {
      surveyStore.updateSurvey(survey.value);
    } else {
      surveyStore.addSurvey(survey.value);
    }
    router.push('/');
  };
  
  // 发布问卷（模拟生成二维码/链接）
  const publish = () => {
    survey.value.status = 'published';
    survey.value.qrCode = 'base64_qrcode_' + survey.value.id; // 实际需生成真实二维码
    survey.value.url = `http://localhost:5173/fill/${survey.value.id}`;
    save();
  };
  </script>
  
  <style scoped>
  .survey-design { padding: 20px; }
  .question-types { display: flex; gap: 10px; margin-bottom: 10px; }
  .questions { display: flex; flex-direction: column; gap: 10px; margin-bottom: 20px; }
  .question-header { display: flex; align-items: center; gap: 10px; margin-bottom: 10px; }
  .option-item { display: flex; align-items: center; gap: 10px; margin-bottom: 10px; }
  .actions { display: flex; gap: 10px; }
  </style>