<template>
    <div class="survey-stats">
      <el-card>
        <h1>{{ survey.title }} - 统计结果</h1>
        <el-divider />
        <div 
          v-for="(q, idx) in survey.questions" 
          :key="q.id" 
          class="stats-item"
        >
          <h3>{{ idx + 1 }}. {{ q.title }}</h3>
          <!-- 单选/多选 → 饼图 -->
          <div 
            v-if="q.type === 'radio' || q.type === 'checkbox'" 
            :id="`chart-${q.id}`" 
            style="width: 100%; height: 400px; margin: 10px 0;"
          ></div>
          <!-- 文本 → 表格展示回答 -->
          <div v-if="q.type === 'text'">
            <el-table :data="q.responses" border style="width: 100%;">
              <el-table-column prop="content" label="回答内容" />
              <el-table-column prop="time" label="提交时间" />
            </el-table>
          </div>
        </div>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, nextTick } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useSurveyStore } from '../store';
  import * as echarts from 'echarts';
  
  const router = useRouter();
  const route = useRoute();
  const surveyStore = useSurveyStore();
  
  const survey = ref({});
  
  onMounted(() => {
    const surveyId = route.params.id;
    const found = surveyStore.getSurveyById(surveyId);
    if (found) {
      survey.value = JSON.parse(JSON.stringify(found));
      nextTick(() => initCharts()); // DOM渲染后初始化ECharts
    } else {
      router.push('/');
    }
  });
  
  // 初始化ECharts图表
  const initCharts = () => {
    survey.value.questions.forEach(q => {
      if (q.type === 'radio' || q.type === 'checkbox') {
        const chartDom = document.getElementById(`chart-${q.id}`);
        if (chartDom) {
          const myChart = echarts.init(chartDom);
          const option = {
            title: { text: q.title },
            tooltip: { trigger: 'item' },
            series: [
              {
                name: '选择人数',
                type: 'pie',
                radius: '50%',
                data: q.options.map(opt => ({
                  value: q.responses?.find(r => r.optionId === opt.id)?.count || 0,
                  name: opt.label,
                })),
              },
            ],
          };
          myChart.setOption(option);
          // 响应式：窗口变化时重绘图表
          window.addEventListener('resize', () => myChart.resize());
        }
      }
    });
  };
  </script>
  
  <style scoped>
  .survey-stats { padding: 20px; }
  h1 { text-align: center; margin-bottom: 10px; }
  .stats-item { margin-bottom: 30px; }
  h3 { margin-bottom: 10px; }
  </style>