import { defineStore } from 'pinia';

export const useSurveyStore = defineStore('survey', {
  state: () => ({
    surveys: [
      {
        id: '1',
        title: '评分问卷调查',
        createTime: '2024-03-02 17:27:12',
        status: 'published',
        questions: [
          {
            id: 'q1',
            type: 'radio', // 单选
            title: '您对产品的满意度？',
            required: true,
            options: [
              { id: 'o1', label: '非常满意' },
              { id: 'o2', label: '满意' },
              { id: 'o3', label: '一般' },
              { id: 'o4', label: '不满意' },
            ],
            responses: [ // 模拟统计数据
              { optionId: 'o1', count: 30 },
              { optionId: 'o2', count: 50 },
              { optionId: 'o3', count: 20 },
              { optionId: 'o4', count: 10 },
            ],
          },
        ],
      },
      // 更多问卷...
    ],
  }),
  actions: {
    getSurveyById(id) {
      return this.surveys.find(s => s.id === id);
    },
    addSurvey(survey) {
      this.surveys.unshift(survey);
    },
    updateSurvey(updated) {
      const idx = this.surveys.findIndex(s => s.id === updated.id);
      if (idx !== -1) this.surveys[idx] = updated;
    },
  },
});