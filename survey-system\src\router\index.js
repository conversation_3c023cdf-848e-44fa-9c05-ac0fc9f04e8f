import { createRouter, createWebHistory } from 'vue-router';
import SurveyList from '../views/SurveyList.vue';
import SurveyDesign from '../views/SurveyDesign.vue';
import SurveyFill from '../views/SurveyFill.vue';
import SurveyStats from '../views/SurveyStats.vue';

const routes = [
  { path: '/', component: SurveyList },          // 问卷列表
  { path: '/design/:id?', component: SurveyDesign }, // 问卷设计（支持新增/编辑）
  { path: '/fill/:id', component: SurveyFill },  // 问卷填写
  { path: '/stats/:id', component: SurveyStats } // 数据统计（可视化）
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;