<template>
    <div class="survey-list">
      <el-card>
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input v-model="searchTitle" placeholder="问卷标题" style="width: 200px;" />
          <el-select v-model="searchStatus" placeholder="状态">
            <el-option label="全部" value="" />
            <el-option label="已发布" value="published" />
          </el-select>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button @click="reset">重置</el-button>
          <el-button type="success" @click="toDesign">+ 新增</el-button>
        </div>
  
        <!-- 问卷表格 -->
        <el-table :data="filteredSurveys" border style="width: 100%;">
          <el-table-column prop="title" label="标题" />
          <el-table-column prop="createTime" label="创建时间" />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'published' ? 'success' : 'info'">
                {{ scope.row.status === 'published' ? '已发布' : '草稿' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="text" @click="toDesign(scope.row.id)">修改</el-button>
              <el-button type="text" @click="toStats(scope.row.id)">统计</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useSurveyStore } from '../store';
  
  const router = useRouter();
  const surveyStore = useSurveyStore();
  
  const searchTitle = ref('');
  const searchStatus = ref('');
  
  // 过滤问卷
  const filteredSurveys = computed(() =>
    surveyStore.surveys.filter(s => 
      s.title.includes(searchTitle.value) && 
      (!searchStatus.value || s.status === searchStatus.value)
    )
  );
  
  const search = () => {/* 触发过滤，computed自动响应 */};
  const reset = () => { searchTitle.value = ''; searchStatus.value = ''; };
  const toDesign = (id) => router.push(`/design/${id || ''}`);
  const toStats = (id) => router.push(`/stats/${id}`);
  </script>
  
  <style scoped>
  .survey-list { padding: 20px; }
  .search-bar { display: flex; gap: 10px; margin-bottom: 10px; }
  </style>